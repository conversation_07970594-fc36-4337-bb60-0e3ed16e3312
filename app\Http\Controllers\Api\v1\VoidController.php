<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Http\Requests\VoidRequest;
use App\Models\ImportFile;
use App\Services\LogService;
use App\Traits\DispenseProManager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VoidController extends Controller
{
    use  DispenseProManager;
    public function void(VoidRequest $request)
    {

        $user = Auth::user();
        $orderId = $request->input('order_id');

        // Check if order_id exists in import_fields table and has status 'Sent'
        $importField = ImportFile::where('order_id', $orderId)->first();

        if (!$importField) {
            return response()->json([
                'message' => __('apiMessages.order_id_not_found'),
                'status' => '0',
            ]);
        }

        // if ($importField->status !== ImportFile::STATUS_SENT) {
        //     return response()->json([
        //         'message' => __('apiMessages.script_not_in_sent_status'),
        //         'status' => '0',
        //     ]);
        // }


        $results = self::DispenseProVoidOrder($orderId);

        $result = $results[0] ?? null;
        $response = $result['response'] ?? [];
        // $status = $response['status'] ?? null;
        $httpStatus = $result['status'] ?? null;

        $errorMessages = $response['errorMessages'] ?? null;

        if ($httpStatus === 200 && (empty($errorMessages) || $errorMessages === [])) {
            $importField->status = ImportFile::STATUS_VOIDED;
            $importField->comment = "void by API";
            $importField->voided_by_user_id = $user->id;
            $importField->save();

            // Log void operation
            LogService::logUserAction('script_voided', 'Script voided successfully', [
                'order_id' => $orderId,
                'void_reason' => $importField->comment ?? null,
                'api_response' => $response
            ], $user);
        } else {

            $errorMessage = !empty($errorMessages) ? implode(', ', $errorMessages) : 'Unknown error occurred';
            LogService::logScriptVoidErrorAPI([
                'order_id' => $orderId,
                'medication' => $importField->medication,
                'script_id' => $importField->id,
                'status' => $importField->status,
                'response' => $response
            ], $errorMessage, $user);
            return response()->json([
                'message' => __('apiMessages.void_failed'),
                'status' => '0',
            ]);
        }
        return response()->json([
            // 'data' => $results,
            'transaction_id' => $request->transaction_id,
            'message' => __('apiMessages.void_successfully'),
            'status' => '1',
        ]);
    }

    public function store(Request $request)
    {
        //
    }
}
